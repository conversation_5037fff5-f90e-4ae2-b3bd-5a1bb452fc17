/**
 * Bayesian Knowledge Tracing (BKT) Core Implementation
 * 
 * This module implements the core BKT algorithm for modeling student knowledge acquisition.
 * BKT is a Hidden Markov Model that tracks the probability of a student knowing a skill
 * based on their performance on exercises.
 * 
 * Key Parameters:
 * - P(L0): Prior probability of knowing the skill initially
 * - P(T): Probability of learning the skill (transition from unknown to known)
 * - P(S): Probability of slipping (knowing but answering incorrectly)
 * - P(G): Probability of guessing (not knowing but answering correctly)
 */

const { clamp } = require('lodash');
const logger = require('../../backend/src/utils/logger');

class BayesianKnowledgeTracing {
  constructor(options = {}) {
    // Default BKT parameters (can be customized per skill/concept)
    this.defaultParams = {
      priorKnowledge: options.priorKnowledge || 0.1,      // P(L0)
      transitProbability: options.transitProbability || 0.1, // P(T)
      slipProbability: options.slipProbability || 0.1,    // P(S)
      guessProbability: options.guessProbability || 0.25,  // P(G)
    };

    // Mastery threshold
    this.masteryThreshold = options.masteryThreshold || 0.8;

    // Parameter bounds for validation
    this.paramBounds = {
      min: 0.001,
      max: 0.999,
    };
  }

  /**
   * Initialize knowledge state for a student-concept pair
   * @param {Object} params - Custom parameters for this concept
   * @returns {Object} Initial knowledge state
   */
  initializeKnowledgeState(params = {}) {
    const initialParams = {
      ...this.defaultParams,
      ...params,
    };

    // Validate parameters
    this.validateParameters(initialParams);

    return {
      masteryProbability: initialParams.priorKnowledge,
      priorKnowledge: initialParams.priorKnowledge,
      transitProbability: initialParams.transitProbability,
      slipProbability: initialParams.slipProbability,
      guessProbability: initialParams.guessProbability,
      attemptsCount: 0,
      correctAttempts: 0,
      masteryAchieved: false,
      lastUpdated: new Date(),
    };
  }

  /**
   * Update knowledge state based on student response
   * @param {Object} knowledgeState - Current knowledge state
   * @param {boolean} isCorrect - Whether the response was correct
   * @param {Object} options - Additional options
   * @returns {Object} Updated knowledge state
   */
  updateKnowledgeState(knowledgeState, isCorrect, options = {}) {
    const startTime = Date.now();

    try {
      // Extract current parameters
      const {
        masteryProbability: currentMastery,
        transitProbability: pTransit,
        slipProbability: pSlip,
        guessProbability: pGuess,
      } = knowledgeState;

      // Calculate probability of correct response given current mastery
      const pCorrectGivenKnown = 1 - pSlip;
      const pCorrectGivenUnknown = pGuess;
      const pCorrect = currentMastery * pCorrectGivenKnown + (1 - currentMastery) * pCorrectGivenUnknown;

      // Update mastery probability using Bayes' theorem
      let newMastery;
      if (isCorrect) {
        // P(Known | Correct) = P(Correct | Known) * P(Known) / P(Correct)
        newMastery = (pCorrectGivenKnown * currentMastery) / pCorrect;
      } else {
        // P(Known | Incorrect) = P(Incorrect | Known) * P(Known) / P(Incorrect)
        const pIncorrectGivenKnown = pSlip;
        const pIncorrect = 1 - pCorrect;
        newMastery = (pIncorrectGivenKnown * currentMastery) / pIncorrect;
      }

      // Apply learning (transition probability)
      // P(Known_after) = P(Known_before) + (1 - P(Known_before)) * P(Transit)
      const finalMastery = newMastery + (1 - newMastery) * pTransit;

      // Clamp to valid probability range
      const clampedMastery = clamp(finalMastery, this.paramBounds.min, this.paramBounds.max);

      // Update attempt counts
      const newAttemptsCount = knowledgeState.attemptsCount + 1;
      const newCorrectAttempts = knowledgeState.correctAttempts + (isCorrect ? 1 : 0);

      // Check for mastery achievement
      const masteryAchieved = clampedMastery >= this.masteryThreshold;

      // Create updated knowledge state
      const updatedState = {
        ...knowledgeState,
        masteryProbability: clampedMastery,
        attemptsCount: newAttemptsCount,
        correctAttempts: newCorrectAttempts,
        masteryAchieved,
        masteryAchievedAt: masteryAchieved && !knowledgeState.masteryAchieved ? new Date() : knowledgeState.masteryAchievedAt,
        lastUpdated: new Date(),
      };

      // Log performance metrics
      const executionTime = Date.now() - startTime;
      logger.logAlgorithmPerformance('BKT_UPDATE', executionTime, 1, {
        previousMastery: currentMastery,
        newMastery: clampedMastery,
        isCorrect,
        masteryAchieved,
      });

      return updatedState;
    } catch (error) {
      logger.error('Error updating BKT knowledge state:', error);
      throw error;
    }
  }

  /**
   * Calculate the probability of a correct response
   * @param {Object} knowledgeState - Current knowledge state
   * @returns {number} Probability of correct response
   */
  predictCorrectProbability(knowledgeState) {
    const {
      masteryProbability,
      slipProbability,
      guessProbability,
    } = knowledgeState;

    const pCorrectGivenKnown = 1 - slipProbability;
    const pCorrectGivenUnknown = guessProbability;

    return masteryProbability * pCorrectGivenKnown + (1 - masteryProbability) * pCorrectGivenUnknown;
  }

  /**
   * Estimate BKT parameters from historical data using Expectation-Maximization
   * @param {Array} responses - Array of response objects {isCorrect, timestamp}
   * @param {Object} initialParams - Initial parameter estimates
   * @returns {Object} Estimated parameters
   */
  estimateParameters(responses, initialParams = {}) {
    if (responses.length < 5) {
      // Not enough data for parameter estimation
      return { ...this.defaultParams, ...initialParams };
    }

    const maxIterations = 100;
    const convergenceThreshold = 0.001;

    let params = { ...this.defaultParams, ...initialParams };
    
    for (let iteration = 0; iteration < maxIterations; iteration++) {
      const newParams = this.emStep(responses, params);
      
      // Check for convergence
      const paramDiff = Math.abs(newParams.priorKnowledge - params.priorKnowledge) +
                       Math.abs(newParams.transitProbability - params.transitProbability) +
                       Math.abs(newParams.slipProbability - params.slipProbability) +
                       Math.abs(newParams.guessProbability - params.guessProbability);

      if (paramDiff < convergenceThreshold) {
        logger.debug(`BKT parameter estimation converged after ${iteration + 1} iterations`);
        break;
      }

      params = newParams;
    }

    return params;
  }

  /**
   * Expectation-Maximization step for parameter estimation
   * @private
   */
  emStep(responses, params) {
    const { priorKnowledge, transitProbability, slipProbability, guessProbability } = params;
    
    // Forward-backward algorithm to compute expected sufficient statistics
    const forwardProbs = this.forwardAlgorithm(responses, params);
    const backwardProbs = this.backwardAlgorithm(responses, params);
    
    // Compute expected counts
    let expectedKnownCount = 0;
    let expectedTransitionCount = 0;
    let expectedSlipCount = 0;
    let expectedGuessCount = 0;
    let totalOpportunities = 0;

    for (let i = 0; i < responses.length; i++) {
      const response = responses[i];
      const knownProb = forwardProbs[i] * backwardProbs[i];
      
      expectedKnownCount += knownProb;
      totalOpportunities += 1;

      if (response.isCorrect) {
        expectedSlipCount += knownProb * slipProbability;
        expectedGuessCount += (1 - knownProb) * guessProbability;
      } else {
        expectedSlipCount += knownProb * (1 - slipProbability);
        expectedGuessCount += (1 - knownProb) * (1 - guessProbability);
      }

      if (i > 0) {
        const prevKnownProb = forwardProbs[i - 1] * backwardProbs[i - 1];
        expectedTransitionCount += (1 - prevKnownProb) * transitProbability;
      }
    }

    // M-step: Update parameters
    const newParams = {
      priorKnowledge: clamp(forwardProbs[0] * backwardProbs[0], this.paramBounds.min, this.paramBounds.max),
      transitProbability: clamp(expectedTransitionCount / Math.max(1, responses.length - 1), this.paramBounds.min, this.paramBounds.max),
      slipProbability: clamp(expectedSlipCount / Math.max(1, expectedKnownCount), this.paramBounds.min, this.paramBounds.max),
      guessProbability: clamp(expectedGuessCount / Math.max(1, totalOpportunities - expectedKnownCount), this.paramBounds.min, this.paramBounds.max),
    };

    return newParams;
  }

  /**
   * Forward algorithm for HMM
   * @private
   */
  forwardAlgorithm(responses, params) {
    const { priorKnowledge, transitProbability, slipProbability, guessProbability } = params;
    const forwardProbs = [];

    for (let i = 0; i < responses.length; i++) {
      const response = responses[i];
      
      if (i === 0) {
        // Initial probability
        const pCorrectGivenKnown = 1 - slipProbability;
        const pCorrectGivenUnknown = guessProbability;
        
        if (response.isCorrect) {
          forwardProbs[i] = priorKnowledge * pCorrectGivenKnown;
        } else {
          forwardProbs[i] = priorKnowledge * slipProbability;
        }
      } else {
        // Transition probability
        const prevKnownProb = forwardProbs[i - 1];
        const currentKnownProb = prevKnownProb + (1 - prevKnownProb) * transitProbability;
        
        const pCorrectGivenKnown = 1 - slipProbability;
        const pCorrectGivenUnknown = guessProbability;
        
        if (response.isCorrect) {
          forwardProbs[i] = currentKnownProb * pCorrectGivenKnown;
        } else {
          forwardProbs[i] = currentKnownProb * slipProbability;
        }
      }
    }

    return forwardProbs;
  }

  /**
   * Backward algorithm for HMM
   * @private
   */
  backwardAlgorithm(responses, params) {
    // Simplified backward algorithm - in practice, this would be more complex
    return responses.map(() => 1.0);
  }

  /**
   * Validate BKT parameters
   * @private
   */
  validateParameters(params) {
    const { priorKnowledge, transitProbability, slipProbability, guessProbability } = params;

    if (priorKnowledge < 0 || priorKnowledge > 1) {
      throw new Error('Prior knowledge must be between 0 and 1');
    }
    if (transitProbability < 0 || transitProbability > 1) {
      throw new Error('Transit probability must be between 0 and 1');
    }
    if (slipProbability < 0 || slipProbability > 1) {
      throw new Error('Slip probability must be between 0 and 1');
    }
    if (guessProbability < 0 || guessProbability > 1) {
      throw new Error('Guess probability must be between 0 and 1');
    }
  }

  /**
   * Get mastery status for a knowledge state
   * @param {Object} knowledgeState - Knowledge state to check
   * @returns {Object} Mastery information
   */
  getMasteryStatus(knowledgeState) {
    return {
      isMastered: knowledgeState.masteryAchieved,
      masteryProbability: knowledgeState.masteryProbability,
      masteryThreshold: this.masteryThreshold,
      attemptsCount: knowledgeState.attemptsCount,
      correctAttempts: knowledgeState.correctAttempts,
      accuracyRate: knowledgeState.attemptsCount > 0 ? knowledgeState.correctAttempts / knowledgeState.attemptsCount : 0,
    };
  }
}

module.exports = BayesianKnowledgeTracing;
