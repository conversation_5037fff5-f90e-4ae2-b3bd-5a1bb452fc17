#!/bin/bash

# AdaptIQ Setup Script
# This script sets up the development environment for AdaptIQ

set -e

echo "🚀 Setting up AdaptIQ Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    print_status "Checking Node.js installation..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js $NODE_VERSION is installed"
        
        # Check if version is >= 18
        NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
            print_error "Node.js version 18 or higher is required. Current version: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
}

# Check if PostgreSQL is installed
check_postgres() {
    print_status "Checking PostgreSQL installation..."
    if command -v psql &> /dev/null; then
        POSTGRES_VERSION=$(psql --version | awk '{print $3}')
        print_success "PostgreSQL $POSTGRES_VERSION is installed"
    else
        print_warning "PostgreSQL is not installed. Please install PostgreSQL 14+ or use Docker"
    fi
}

# Check if Redis is installed
check_redis() {
    print_status "Checking Redis installation..."
    if command -v redis-cli &> /dev/null; then
        REDIS_VERSION=$(redis-cli --version | awk '{print $2}')
        print_success "Redis $REDIS_VERSION is installed"
    else
        print_warning "Redis is not installed. Please install Redis 6+ or use Docker"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing root dependencies..."
    npm install

    print_status "Installing backend dependencies..."
    cd backend && npm install && cd ..

    print_status "Installing frontend dependencies..."
    cd frontend && npm install && cd ..

    print_status "Installing algorithm dependencies..."
    cd algorithms && npm install && cd ..

    print_success "All dependencies installed successfully!"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please edit .env file with your configuration"
    else
        print_warning ".env file already exists"
    fi
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if we can connect to PostgreSQL
    if command -v psql &> /dev/null; then
        print_status "Creating database and running migrations..."
        cd backend
        npm run db:migrate 2>/dev/null || print_warning "Database migration failed. Please check your database configuration."
        npm run db:seed 2>/dev/null || print_warning "Database seeding failed. Please check your database configuration."
        cd ..
    else
        print_warning "PostgreSQL not available. Skipping database setup."
        print_warning "You can use Docker: docker-compose up postgres redis"
    fi
}

# Setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."
    if [ -d .git ]; then
        npx husky install
        print_success "Git hooks installed"
    else
        print_warning "Not a Git repository. Skipping Git hooks setup."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p backend/temp
    mkdir -p frontend/public/uploads
    mkdir -p algorithms/benchmarks
    mkdir -p algorithms/test-data
    
    print_success "Directories created"
}

# Main setup function
main() {
    echo "=================================="
    echo "   AdaptIQ Setup Script v1.0.0   "
    echo "=================================="
    echo ""
    
    check_node
    check_postgres
    check_redis
    
    echo ""
    print_status "Starting installation process..."
    
    create_directories
    setup_environment
    install_dependencies
    setup_database
    setup_git_hooks
    
    echo ""
    print_success "🎉 AdaptIQ setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Start the development servers: npm run dev"
    echo "3. Visit http://localhost:3000 to see the application"
    echo ""
    echo "For Docker setup: docker-compose up -d"
    echo ""
    print_status "Happy coding! 🚀"
}

# Run main function
main "$@"
