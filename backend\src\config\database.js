/**
 * Database Configuration
 * Handles PostgreSQL and Redis connections
 */

const { Pool } = require('pg');
const Redis = require('ioredis');
const logger = require('../utils/logger');

// PostgreSQL configuration
const pgConfig = {
  connectionString: process.env.DATABASE_URL,
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'adaptiq_db',
  user: process.env.DB_USER || 'adaptiq_user',
  password: process.env.DB_PASSWORD,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
};

// Create PostgreSQL connection pool
const pool = new Pool(pgConfig);

// PostgreSQL connection event handlers
pool.on('connect', () => {
  logger.info('📊 Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  logger.error('PostgreSQL connection error:', err);
});

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  lazyConnect: true,
};

// Create Redis connection
const redis = new Redis(redisConfig);

// Redis connection event handlers
redis.on('connect', () => {
  logger.info('🔴 Connected to Redis cache');
});

redis.on('error', (err) => {
  logger.error('Redis connection error:', err);
});

redis.on('ready', () => {
  logger.info('🔴 Redis is ready');
});

// Database helper functions
const db = {
  // PostgreSQL query helper
  query: async (text, params) => {
    const start = Date.now();
    try {
      const result = await pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug(`Executed query in ${duration}ms: ${text}`);
      return result;
    } catch (error) {
      logger.error('Database query error:', error);
      throw error;
    }
  },

  // Get a client from the pool for transactions
  getClient: async () => {
    try {
      const client = await pool.connect();
      return client;
    } catch (error) {
      logger.error('Error getting database client:', error);
      throw error;
    }
  },

  // Transaction helper
  transaction: async (callback) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Transaction error:', error);
      throw error;
    } finally {
      client.release();
    }
  },

  // Redis helpers
  cache: {
    get: async (key) => {
      try {
        const value = await redis.get(key);
        return value ? JSON.parse(value) : null;
      } catch (error) {
        logger.error('Redis get error:', error);
        return null;
      }
    },

    set: async (key, value, ttl = 3600) => {
      try {
        const serialized = JSON.stringify(value);
        if (ttl) {
          await redis.setex(key, ttl, serialized);
        } else {
          await redis.set(key, serialized);
        }
        return true;
      } catch (error) {
        logger.error('Redis set error:', error);
        return false;
      }
    },

    del: async (key) => {
      try {
        await redis.del(key);
        return true;
      } catch (error) {
        logger.error('Redis delete error:', error);
        return false;
      }
    },

    exists: async (key) => {
      try {
        const result = await redis.exists(key);
        return result === 1;
      } catch (error) {
        logger.error('Redis exists error:', error);
        return false;
      }
    },

    expire: async (key, ttl) => {
      try {
        await redis.expire(key, ttl);
        return true;
      } catch (error) {
        logger.error('Redis expire error:', error);
        return false;
      }
    },
  },
};

// Test database connections
const testConnections = async () => {
  try {
    // Test PostgreSQL
    await pool.query('SELECT NOW()');
    logger.info('✅ PostgreSQL connection test successful');

    // Test Redis
    await redis.ping();
    logger.info('✅ Redis connection test successful');
  } catch (error) {
    logger.error('❌ Database connection test failed:', error);
  }
};

// Initialize connections
const initializeDatabase = async () => {
  try {
    await testConnections();
    logger.info('🗄️ Database connections initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize database connections:', error);
    process.exit(1);
  }
};

// Graceful shutdown
const closeConnections = async () => {
  try {
    await pool.end();
    await redis.quit();
    logger.info('🔌 Database connections closed');
  } catch (error) {
    logger.error('Error closing database connections:', error);
  }
};

module.exports = {
  pool,
  redis,
  db,
  initializeDatabase,
  closeConnections,
};
