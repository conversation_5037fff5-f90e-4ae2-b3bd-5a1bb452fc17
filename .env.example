# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/adaptiq_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=adaptiq_db
DB_USER=adaptiq_user
DB_PASSWORD=your_secure_password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Server Configuration
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_here
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your_session_secret_key_here

# LTI Configuration
LTI_KEY=your_lti_consumer_key
LTI_SECRET=your_lti_consumer_secret
LTI_LAUNCH_URL=http://localhost:5000/api/lti/launch

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# External API Keys (Free APIs)
OPENAI_API_KEY=your_openai_api_key_if_needed
WIKIPEDIA_API_URL=https://en.wikipedia.org/api/rest_v1
KHAN_ACADEMY_API_URL=https://www.khanacademy.org/api

# Analytics Configuration
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
MIXPANEL_TOKEN=your_mixpanel_token

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Algorithm Configuration
BKT_DEFAULT_PRIOR=0.1
BKT_DEFAULT_TRANSIT=0.1
BKT_DEFAULT_SLIP=0.1
BKT_DEFAULT_GUESS=0.25

SRS_DEFAULT_EASE=2.5
SRS_DEFAULT_INTERVAL=1
SRS_MIN_EASE=1.3
SRS_MAX_INTERVAL=36500

# Content Configuration
DEFAULT_DIFFICULTY_LEVEL=1
MAX_DIFFICULTY_LEVEL=10
MASTERY_THRESHOLD=0.8

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Development Configuration
DEBUG=adaptiq:*
VERBOSE_LOGGING=false

# Testing Configuration
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/adaptiq_test_db
TEST_REDIS_URL=redis://localhost:6379/1

# Docker Configuration
POSTGRES_DB=adaptiq_db
POSTGRES_USER=adaptiq_user
POSTGRES_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
