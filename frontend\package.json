{"name": "adaptiq-frontend", "version": "1.0.0", "description": "Frontend React application for AdaptIQ Adaptive Learning Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest --coverage", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "cross-env ANALYZE=true next build", "docker:build": "docker build -t adaptiq-frontend .", "docker:run": "docker run -p 3000:3000 adaptiq-frontend"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "date-fns": "^2.30.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "eslint": "^8.57.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.1", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/*.stories.{js,jsx,ts,tsx}"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}