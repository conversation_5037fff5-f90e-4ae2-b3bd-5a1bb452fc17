/**
 * Logger Configuration
 * Winston-based logging system with multiple transports
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }),
];

// Add file transports in production
if (process.env.NODE_ENV === 'production' || process.env.LOG_FILE) {
  // Error log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(process.cwd(), 'logs', 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      handleExceptions: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );

  // Combined log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(process.cwd(), 'logs', 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );

  // HTTP log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(process.cwd(), 'logs', 'http-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'http',
      maxSize: '20m',
      maxFiles: '7d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  format,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
logger.logRequest = (req, res, responseTime) => {
  logger.http(`${req.method} ${req.originalUrl} ${res.statusCode} - ${responseTime}ms`);
};

logger.logError = (error, req = null) => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
  };

  if (req) {
    errorInfo.request = {
      method: req.method,
      url: req.originalUrl,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    };
  }

  logger.error(JSON.stringify(errorInfo, null, 2));
};

logger.logUserAction = (userId, action, details = {}) => {
  logger.info(`User ${userId} performed action: ${action}`, {
    userId,
    action,
    details,
    timestamp: new Date().toISOString(),
  });
};

logger.logAlgorithmPerformance = (algorithm, executionTime, inputSize, result) => {
  logger.debug(`Algorithm ${algorithm} executed in ${executionTime}ms`, {
    algorithm,
    executionTime,
    inputSize,
    result: typeof result === 'object' ? JSON.stringify(result) : result,
    timestamp: new Date().toISOString(),
  });
};

logger.logDatabaseQuery = (query, params, executionTime) => {
  if (process.env.LOG_LEVEL === 'debug') {
    logger.debug(`Database query executed in ${executionTime}ms`, {
      query,
      params,
      executionTime,
      timestamp: new Date().toISOString(),
    });
  }
};

logger.logCacheOperation = (operation, key, hit = null) => {
  logger.debug(`Cache ${operation}: ${key}${hit !== null ? ` (${hit ? 'HIT' : 'MISS'})` : ''}`, {
    operation,
    key,
    hit,
    timestamp: new Date().toISOString(),
  });
};

// Export logger
module.exports = logger;
