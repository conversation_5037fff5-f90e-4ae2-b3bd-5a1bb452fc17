/**
 * Spaced Repetition System (SRS) - Anki Algorithm Implementation
 * 
 * This module implements the SM-2 algorithm used by <PERSON><PERSON> for spaced repetition.
 * The algorithm optimally schedules review sessions based on the forgetting curve
 * and user performance to maximize long-term retention.
 * 
 * Key Components:
 * - Ease Factor: Determines how much the interval increases after successful reviews
 * - Interval: Days until the next review
 * - Repetition Number: Count of successful reviews in a row
 * - Quality Response: User's self-assessment of recall difficulty (0-5)
 */

const moment = require('moment');
const { clamp } = require('lodash');
const logger = require('../../backend/src/utils/logger');

class AnkiAlgorithm {
  constructor(options = {}) {
    // Default algorithm parameters
    this.defaultParams = {
      initialEase: options.initialEase || 2.5,
      minEase: options.minEase || 1.3,
      maxEase: options.maxEase || 5.0,
      easeBonus: options.easeBonus || 0.15,
      easePenalty: options.easePenalty || 0.20,
      hardMultiplier: options.hardMultiplier || 1.2,
      goodMultiplier: options.goodMultiplier || 1.0,
      easyMultiplier: options.easyMultiplier || 1.3,
      lapseMultiplier: options.lapseMultiplier || 0.0,
      minimumInterval: options.minimumInterval || 1,
      maximumInterval: options.maximumInterval || 36500, // ~100 years
      graduatingInterval: options.graduatingInterval || 1,
      easyInterval: options.easyInterval || 4,
    };

    // Learning steps for new cards (in minutes)
    this.learningSteps = options.learningSteps || [1, 10];
    
    // Relearning steps for lapsed cards (in minutes)
    this.relearningSteps = options.relearningSteps || [10];
  }

  /**
   * Initialize a new SRS item
   * @param {Object} options - Custom parameters
   * @returns {Object} Initial SRS state
   */
  initializeSRSItem(options = {}) {
    return {
      easeFactor: this.defaultParams.initialEase,
      interval: 0,
      repetitionNumber: 0,
      isLearning: true,
      isRelearning: false,
      learningStep: 0,
      nextReviewDate: new Date(),
      lastReviewed: null,
      totalReviews: 0,
      lapseCount: 0,
      averageQuality: 0,
      created: new Date(),
      ...options,
    };
  }

  /**
   * Process a review response and update the SRS item
   * @param {Object} srsItem - Current SRS item state
   * @param {number} quality - Quality response (0-5)
   * @param {Date} reviewDate - Date of the review (defaults to now)
   * @returns {Object} Updated SRS item
   */
  processReview(srsItem, quality, reviewDate = new Date()) {
    const startTime = Date.now();

    try {
      // Validate quality response
      quality = clamp(Math.round(quality), 0, 5);

      // Clone the item to avoid mutation
      const updatedItem = { ...srsItem };
      updatedItem.lastReviewed = reviewDate;
      updatedItem.totalReviews += 1;

      // Update average quality
      const totalQualitySum = (srsItem.averageQuality * srsItem.totalReviews) + quality;
      updatedItem.averageQuality = totalQualitySum / updatedItem.totalReviews;

      // Process based on current state
      if (updatedItem.isLearning) {
        this.processLearningReview(updatedItem, quality, reviewDate);
      } else if (updatedItem.isRelearning) {
        this.processRelearningReview(updatedItem, quality, reviewDate);
      } else {
        this.processReviewReview(updatedItem, quality, reviewDate);
      }

      // Log performance
      const executionTime = Date.now() - startTime;
      logger.logAlgorithmPerformance('SRS_REVIEW', executionTime, 1, {
        quality,
        newInterval: updatedItem.interval,
        easeFactor: updatedItem.easeFactor,
        isLearning: updatedItem.isLearning,
      });

      return updatedItem;
    } catch (error) {
      logger.error('Error processing SRS review:', error);
      throw error;
    }
  }

  /**
   * Process review for learning cards (new cards)
   * @private
   */
  processLearningReview(item, quality, reviewDate) {
    if (quality < 3) {
      // Failed - restart learning
      item.learningStep = 0;
      item.nextReviewDate = this.addMinutes(reviewDate, this.learningSteps[0]);
    } else {
      // Passed - advance to next step
      item.learningStep += 1;

      if (item.learningStep >= this.learningSteps.length) {
        // Graduated from learning
        item.isLearning = false;
        item.repetitionNumber = 1;
        
        if (quality === 3) {
          // Good - use graduating interval
          item.interval = this.defaultParams.graduatingInterval;
        } else {
          // Easy - use easy interval
          item.interval = this.defaultParams.easyInterval;
          item.easeFactor += this.defaultParams.easeBonus;
        }
        
        item.nextReviewDate = this.addDays(reviewDate, item.interval);
      } else {
        // Continue learning - next step
        const nextStepMinutes = this.learningSteps[item.learningStep];
        item.nextReviewDate = this.addMinutes(reviewDate, nextStepMinutes);
      }
    }
  }

  /**
   * Process review for relearning cards (lapsed cards)
   * @private
   */
  processRelearningReview(item, quality, reviewDate) {
    if (quality < 3) {
      // Failed - restart relearning
      item.learningStep = 0;
      item.nextReviewDate = this.addMinutes(reviewDate, this.relearningSteps[0]);
    } else {
      // Passed - advance relearning
      item.learningStep += 1;

      if (item.learningStep >= this.relearningSteps.length) {
        // Graduated from relearning
        item.isRelearning = false;
        item.interval = Math.max(1, Math.round(item.interval * this.defaultParams.lapseMultiplier));
        item.nextReviewDate = this.addDays(reviewDate, item.interval);
      } else {
        // Continue relearning
        const nextStepMinutes = this.relearningSteps[item.learningStep];
        item.nextReviewDate = this.addMinutes(reviewDate, nextStepMinutes);
      }
    }
  }

  /**
   * Process review for mature cards (regular reviews)
   * @private
   */
  processReviewReview(item, quality, reviewDate) {
    if (quality < 3) {
      // Lapsed - enter relearning
      item.isRelearning = true;
      item.learningStep = 0;
      item.lapseCount += 1;
      item.repetitionNumber = 0;
      
      // Reduce ease factor
      item.easeFactor = Math.max(
        this.defaultParams.minEase,
        item.easeFactor - this.defaultParams.easePenalty
      );
      
      item.nextReviewDate = this.addMinutes(reviewDate, this.relearningSteps[0]);
    } else {
      // Successful review
      item.repetitionNumber += 1;

      // Calculate new interval based on quality
      let intervalMultiplier;
      switch (quality) {
        case 3: // Hard
          intervalMultiplier = this.defaultParams.hardMultiplier;
          item.easeFactor = Math.max(
            this.defaultParams.minEase,
            item.easeFactor - this.defaultParams.easePenalty
          );
          break;
        case 4: // Good
          intervalMultiplier = this.defaultParams.goodMultiplier;
          break;
        case 5: // Easy
          intervalMultiplier = this.defaultParams.easyMultiplier;
          item.easeFactor = Math.min(
            this.defaultParams.maxEase,
            item.easeFactor + this.defaultParams.easeBonus
          );
          break;
        default:
          intervalMultiplier = this.defaultParams.goodMultiplier;
      }

      // Calculate new interval
      if (item.repetitionNumber === 1) {
        item.interval = this.defaultParams.graduatingInterval;
      } else if (item.repetitionNumber === 2) {
        item.interval = 6;
      } else {
        item.interval = Math.round(item.interval * item.easeFactor * intervalMultiplier);
      }

      // Apply interval bounds
      item.interval = clamp(
        item.interval,
        this.defaultParams.minimumInterval,
        this.defaultParams.maximumInterval
      );

      item.nextReviewDate = this.addDays(reviewDate, item.interval);
    }
  }

  /**
   * Get items due for review
   * @param {Array} srsItems - Array of SRS items
   * @param {Date} currentDate - Current date (defaults to now)
   * @param {number} limit - Maximum number of items to return
   * @returns {Array} Items due for review, sorted by priority
   */
  getDueItems(srsItems, currentDate = new Date(), limit = null) {
    const dueItems = srsItems.filter(item => {
      return new Date(item.nextReviewDate) <= currentDate;
    });

    // Sort by priority (overdue items first, then by interval)
    dueItems.sort((a, b) => {
      const aOverdue = currentDate - new Date(a.nextReviewDate);
      const bOverdue = currentDate - new Date(b.nextReviewDate);

      // Prioritize more overdue items
      if (aOverdue !== bOverdue) {
        return bOverdue - aOverdue;
      }

      // Then prioritize learning/relearning items
      if (a.isLearning !== b.isLearning) {
        return a.isLearning ? -1 : 1;
      }

      if (a.isRelearning !== b.isRelearning) {
        return a.isRelearning ? -1 : 1;
      }

      // Finally, prioritize shorter intervals (more frequent reviews)
      return a.interval - b.interval;
    });

    return limit ? dueItems.slice(0, limit) : dueItems;
  }

  /**
   * Calculate retention rate for an SRS item
   * @param {Object} srsItem - SRS item
   * @param {Date} currentDate - Current date
   * @returns {number} Estimated retention rate (0-1)
   */
  calculateRetentionRate(srsItem, currentDate = new Date()) {
    if (srsItem.isLearning || srsItem.isRelearning) {
      return 0.5; // Default for learning items
    }

    const daysSinceReview = moment(currentDate).diff(moment(srsItem.lastReviewed), 'days');
    const stabilityFactor = srsItem.interval * srsItem.easeFactor;
    
    // Exponential decay model
    const retentionRate = Math.exp(-daysSinceReview / stabilityFactor);
    
    return clamp(retentionRate, 0.1, 0.95);
  }

  /**
   * Predict optimal review schedule
   * @param {Object} srsItem - SRS item
   * @param {number} targetRetention - Target retention rate (0-1)
   * @returns {Object} Schedule prediction
   */
  predictOptimalSchedule(srsItem, targetRetention = 0.9) {
    const predictions = [];
    let currentItem = { ...srsItem };
    const currentDate = new Date();

    // Simulate different quality responses
    for (let quality = 3; quality <= 5; quality++) {
      const simulatedItem = this.processReview({ ...currentItem }, quality, currentDate);
      const retentionRate = this.calculateRetentionRate(simulatedItem, simulatedItem.nextReviewDate);
      
      predictions.push({
        quality,
        nextReviewDate: simulatedItem.nextReviewDate,
        interval: simulatedItem.interval,
        easeFactor: simulatedItem.easeFactor,
        estimatedRetention: retentionRate,
        recommendedAction: this.getRecommendedAction(quality, retentionRate, targetRetention),
      });
    }

    return {
      currentRetention: this.calculateRetentionRate(srsItem, currentDate),
      predictions,
      optimalQuality: this.findOptimalQuality(predictions, targetRetention),
    };
  }

  /**
   * Get recommended action based on quality and retention
   * @private
   */
  getRecommendedAction(quality, retentionRate, targetRetention) {
    if (retentionRate < targetRetention * 0.8) {
      return 'review_more_frequently';
    } else if (retentionRate > targetRetention * 1.2) {
      return 'extend_interval';
    } else {
      return 'maintain_schedule';
    }
  }

  /**
   * Find optimal quality response for target retention
   * @private
   */
  findOptimalQuality(predictions, targetRetention) {
    let bestQuality = 4;
    let bestDifference = Infinity;

    predictions.forEach(prediction => {
      const difference = Math.abs(prediction.estimatedRetention - targetRetention);
      if (difference < bestDifference) {
        bestDifference = difference;
        bestQuality = prediction.quality;
      }
    });

    return bestQuality;
  }

  /**
   * Get statistics for an SRS item
   * @param {Object} srsItem - SRS item
   * @returns {Object} Statistics
   */
  getItemStatistics(srsItem) {
    const currentDate = new Date();
    const daysSinceCreated = moment(currentDate).diff(moment(srsItem.created), 'days');
    const daysSinceLastReview = srsItem.lastReviewed 
      ? moment(currentDate).diff(moment(srsItem.lastReviewed), 'days')
      : null;

    return {
      totalReviews: srsItem.totalReviews,
      repetitionNumber: srsItem.repetitionNumber,
      lapseCount: srsItem.lapseCount,
      averageQuality: srsItem.averageQuality,
      currentInterval: srsItem.interval,
      easeFactor: srsItem.easeFactor,
      isLearning: srsItem.isLearning,
      isRelearning: srsItem.isRelearning,
      daysSinceCreated,
      daysSinceLastReview,
      estimatedRetention: this.calculateRetentionRate(srsItem, currentDate),
      nextReviewDate: srsItem.nextReviewDate,
      isOverdue: new Date(srsItem.nextReviewDate) < currentDate,
    };
  }

  /**
   * Helper function to add minutes to a date
   * @private
   */
  addMinutes(date, minutes) {
    return moment(date).add(minutes, 'minutes').toDate();
  }

  /**
   * Helper function to add days to a date
   * @private
   */
  addDays(date, days) {
    return moment(date).add(days, 'days').toDate();
  }
}

module.exports = AnkiAlgorithm;
