/**
 * Authentication Routes
 * Handles user registration, login, logout, and token refresh
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { 
  generateToken, 
  generateRefreshToken, 
  refreshToken, 
  logout, 
  authenticate 
} = require('../middleware/auth');
const { catchAsync, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation middleware
const validateRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('role')
    .optional()
    .isIn(['student', 'instructor', 'admin'])
    .withMessage('Role must be student, instructor, or admin'),
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

const validateRefreshToken = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
];

// Helper function to check validation results
const checkValidation = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    throw new ValidationError('Validation failed', errorMessages);
  }
  next();
};

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', validateRegistration, checkValidation, catchAsync(async (req, res) => {
  const { email, password, firstName, lastName, role } = req.body;

  // Create new user
  const user = await User.create({
    email,
    password,
    firstName,
    lastName,
    role: role || 'student',
  });

  // Generate tokens
  const token = generateToken({ userId: user.id, email: user.email });
  const refreshTokenValue = generateRefreshToken({ userId: user.id });

  logger.logUserAction(user.id, 'USER_REGISTERED', { email, role: user.role });

  res.status(201).json({
    status: 'success',
    message: 'User registered successfully',
    data: {
      user: user.toJSON(),
      token,
      refreshToken: refreshTokenValue,
    },
  });
}));

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', validateLogin, checkValidation, catchAsync(async (req, res) => {
  const { email, password } = req.body;

  // Find user by email
  const user = await User.findByEmail(email);
  if (!user) {
    logger.logUserAction(null, 'LOGIN_FAILED', { email, reason: 'User not found' });
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid email or password',
    });
  }

  // Check if user is active
  if (!user.isActive) {
    logger.logUserAction(user.id, 'LOGIN_FAILED', { email, reason: 'Account deactivated' });
    return res.status(401).json({
      status: 'fail',
      message: 'Account is deactivated. Please contact support.',
    });
  }

  // Verify password
  const isPasswordValid = await user.verifyPassword(password);
  if (!isPasswordValid) {
    logger.logUserAction(user.id, 'LOGIN_FAILED', { email, reason: 'Invalid password' });
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid email or password',
    });
  }

  // Generate tokens
  const token = generateToken({ userId: user.id, email: user.email });
  const refreshTokenValue = generateRefreshToken({ userId: user.id });

  logger.logUserAction(user.id, 'USER_LOGGED_IN', { email });

  res.json({
    status: 'success',
    message: 'Login successful',
    data: {
      user: user.toJSON(),
      token,
      refreshToken: refreshTokenValue,
    },
  });
}));

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', validateRefreshToken, checkValidation, refreshToken, catchAsync(async (req, res) => {
  logger.logUserAction(req.user.id, 'TOKEN_REFRESHED');

  res.json({
    status: 'success',
    message: 'Token refreshed successfully',
    data: {
      user: req.user,
      token: req.newToken,
      refreshToken: req.newRefreshToken,
    },
  });
}));

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (blacklist token)
 * @access  Private
 */
router.post('/logout', authenticate, logout, catchAsync(async (req, res) => {
  logger.logUserAction(req.user.id, 'USER_LOGGED_OUT');

  res.json({
    status: 'success',
    message: 'Logout successful',
  });
}));

/**
 * @route   GET /api/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', authenticate, catchAsync(async (req, res) => {
  // Get fresh user data from database
  const user = await User.findById(req.user.id);
  if (!user) {
    return res.status(404).json({
      status: 'fail',
      message: 'User not found',
    });
  }

  // Get learning progress
  const progress = await user.getLearningProgress();

  res.json({
    status: 'success',
    data: {
      user: user.toJSON(),
      progress,
    },
  });
}));

/**
 * @route   PUT /api/auth/change-password
 * @desc    Change user password
 * @access  Private
 */
router.put('/change-password', 
  authenticate,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
  ],
  checkValidation,
  catchAsync(async (req, res) => {
    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        status: 'fail',
        message: 'User not found',
      });
    }

    await user.changePassword(currentPassword, newPassword);

    res.json({
      status: 'success',
      message: 'Password changed successfully',
    });
  })
);

/**
 * @route   GET /api/auth/verify
 * @desc    Verify token validity
 * @access  Private
 */
router.get('/verify', authenticate, catchAsync(async (req, res) => {
  res.json({
    status: 'success',
    message: 'Token is valid',
    data: {
      user: req.user,
    },
  });
}));

module.exports = router;
