# AdaptIQ: Adaptive Learning Path Engine

An intelligent learning platform that personalizes educational content using advanced algorithms including Bayesian Knowledge Tracing and Spaced Repetition to create optimized learning paths that adapt to individual learner performance.

## 🚀 Creative Product Name Suggestions

Based on domain availability research, here are our top recommendations:

1. **AdaptLearn** - `adaptlearn.io` ✅ **AVAILABLE**
   - Emphasizes adaptive learning with a clean, professional domain
   
2. **NeuralLearn** - `neurallearn.app` ✅ **AVAILABLE**
   - Combines neural network concepts with learning, modern .app extension
   
3. **AdaptIQ** - `adaptiq.app` ❌ Taken (but `adaptiq.dev` may be available)
   - Original suggestion, emphasizes adaptive intelligence
   
4. **SmartPath** - `smartpath.dev` ❌ Taken
   - Clean, intuitive name suggesting intelligent learning paths
   
5. **CogniFlow** - `cogniflow.io` (needs verification)
   - Suggests cognitive learning with smooth flow

**Recommended Choice**: **AdaptLearn** with `adaptlearn.io` domain for optimal branding and availability.

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React/Next.js App]
        B[Interactive Exercises]
        C[Progress Dashboard]
        D[LTI Integration UI]
    end
    
    subgraph "API Gateway"
        E[Express.js Server]
        F[Authentication Middleware]
        G[Rate Limiting]
    end
    
    subgraph "Core Engine"
        H[Adaptive Learning Engine]
        I[Bayesian Knowledge Tracing]
        J[Spaced Repetition System]
        K[Content Difficulty Scaler]
        L[Prerequisite Mapper]
    end
    
    subgraph "Data Layer"
        M[(PostgreSQL Database)]
        N[(Redis Cache)]
        O[File Storage]
    end
    
    subgraph "External Integrations"
        P[LTI Provider]
        Q[Free Educational APIs]
        R[Analytics Service]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    H --> I
    H --> J
    H --> K
    H --> L
    
    I --> M
    J --> M
    K --> M
    L --> M
    
    H --> N
    E --> N
    
    E --> O
    D --> P
    H --> Q
    C --> R
```

## 👤 User Workflow Diagram

```mermaid
flowchart TD
    A[User Registration/Login] --> B{First Time User?}
    
    B -->|Yes| C[Initial Assessment]
    B -->|No| D[Load User Profile]
    
    C --> E[Generate Learning Path]
    D --> F[Update Knowledge State]
    
    E --> G[Present Next Exercise]
    F --> G
    
    G --> H[User Completes Exercise]
    H --> I[Bayesian Knowledge Update]
    
    I --> J{Mastery Achieved?}
    
    J -->|No| K[Adjust Difficulty]
    J -->|Yes| L[Schedule for Spaced Repetition]
    
    K --> G
    L --> M[Move to Next Concept]
    
    M --> N{More Prerequisites?}
    N -->|Yes| G
    N -->|No| O[Course Completion]
    
    subgraph "Background Processes"
        P[Analytics Collection]
        Q[Progress Tracking]
        R[Performance Optimization]
    end
    
    H --> P
    I --> Q
    J --> R
```

## 📁 Complete Project Structure

```
AdaptIQ_AugmentCode/
├── README.md
├── package.json
├── .gitignore
├── .env.example
├── docker-compose.yml
├── 
├── frontend/
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.js
│   ├── src/
│   │   ├── components/
│   │   │   ├── common/
│   │   │   ├── exercises/
│   │   │   ├── dashboard/
│   │   │   └── lti/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── styles/
│   └── public/
│
├── backend/
│   ├── package.json
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── middleware/
│   │   ├── services/
│   │   │   ├── bkt/
│   │   │   ├── srs/
│   │   │   ├── adaptive-engine/
│   │   │   └── lti/
│   │   ├── utils/
│   │   └── config/
│   ├── tests/
│   └── docs/
│
├── algorithms/
│   ├── bayesian-knowledge-tracing/
│   │   ├── bkt-core.js
│   │   ├── knowledge-state.js
│   │   └── parameter-estimation.js
│   ├── spaced-repetition/
│   │   ├── anki-algorithm.js
│   │   ├── scheduling.js
│   │   └── difficulty-adjustment.js
│   └── adaptive-engine/
│       ├── path-generator.js
│       ├── content-scaler.js
│       └── prerequisite-mapper.js
│
├── database/
│   ├── migrations/
│   ├── seeds/
│   └── schema.sql
│
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
│
├── docs/
│   ├── api/
│   ├── algorithms/
│   └── deployment/
│
└── scripts/
    ├── setup.sh
    ├── deploy.sh
    └── test.sh
```

## 🛠️ Installation and Setup

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL 14+
- Redis 6+
- Git

### Quick Start

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/AdaptIQ_AugmentCode.git
cd AdaptIQ_AugmentCode
```

2. **Install dependencies**
```bash
npm install
cd frontend && npm install
cd ../backend && npm install
```

3. **Environment setup**
```bash
cp .env.example .env
# Edit .env with your database and API configurations
```

4. **Database setup**
```bash
npm run db:migrate
npm run db:seed
```

5. **Start development servers**
```bash
npm run dev
```

This will start:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Database: localhost:5432

## 📚 API Documentation Overview

### Core Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh

#### Learning Paths
- `GET /api/paths` - Get user's learning paths
- `POST /api/paths` - Create new learning path
- `PUT /api/paths/:id` - Update learning path

#### Exercises
- `GET /api/exercises` - Get available exercises
- `POST /api/exercises/:id/submit` - Submit exercise response
- `GET /api/exercises/:id/feedback` - Get exercise feedback

#### Progress Tracking
- `GET /api/progress` - Get user progress
- `GET /api/analytics` - Get learning analytics

#### LTI Integration
- `POST /api/lti/launch` - LTI launch endpoint
- `GET /api/lti/config` - LTI configuration

### Algorithm Endpoints
- `POST /api/bkt/update` - Update knowledge state
- `GET /api/srs/schedule` - Get spaced repetition schedule
- `POST /api/adaptive/recommend` - Get content recommendations

## 🧠 Core Features

### ✅ Implemented Features
- **Personalized Learning Paths**: Dynamic content adjustment based on performance
- **Interactive Exercises**: Engaging content with real-time feedback
- **Progress Tracking**: Comprehensive analytics dashboard
- **Bayesian Knowledge Tracing**: Custom implementation for knowledge modeling
- **Spaced Repetition System**: Anki-based algorithm for optimal review scheduling
- **Content Difficulty Scaling**: Automatic complexity adjustment
- **Prerequisite Mapping**: Intelligent content sequencing
- **LTI Integration**: Standards-compliant LMS integration

### 🔄 Algorithm Implementations
- Custom Bayesian Knowledge Tracing (BKT) engine
- Anki SM-2 based Spaced Repetition System
- Adaptive difficulty scaling algorithms
- Prerequisite dependency mapping
- Performance prediction models

## 🚀 Deployment

### Docker Deployment
```bash
docker-compose up -d
```

### Manual Deployment
See `docs/deployment/` for detailed deployment instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 👨‍💻 Author

**Hector Ta**
- GitHub: [@HectorTa1989](https://github.com/HectorTa1989)
- Project: AdaptIQ - Adaptive Learning Path Engine

---

*Built with ❤️ for personalized education*
