/**
 * User Model
 * Handles user-related database operations
 */

const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const { ValidationError, ConflictError, NotFoundError } = require('../middleware/errorHandler');

class User {
  constructor(data) {
    this.id = data.id;
    this.email = data.email;
    this.password = data.password;
    this.firstName = data.first_name;
    this.lastName = data.last_name;
    this.role = data.role || 'student';
    this.isActive = data.is_active !== false;
    this.profilePicture = data.profile_picture;
    this.preferences = data.preferences || {};
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
  }

  // Create a new user
  static async create(userData) {
    const { email, password, firstName, lastName, role = 'student' } = userData;

    // Validate required fields
    if (!email || !password || !firstName || !lastName) {
      throw new ValidationError('Email, password, first name, and last name are required');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError('Invalid email format');
    }

    // Validate password strength
    if (password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    try {
      // Check if user already exists
      const existingUser = await db.query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUser.rows.length > 0) {
        throw new ConflictError('User with this email already exists');
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Generate user ID
      const userId = uuidv4();

      // Insert user into database
      const result = await db.query(
        `INSERT INTO users (id, email, password, first_name, last_name, role, is_active, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
         RETURNING id, email, first_name, last_name, role, is_active, created_at, updated_at`,
        [userId, email.toLowerCase(), hashedPassword, firstName, lastName, role, true]
      );

      const user = new User(result.rows[0]);
      
      logger.logUserAction(userId, 'USER_CREATED', { email, role });
      
      return user;
    } catch (error) {
      if (error.code === '23505') {
        throw new ConflictError('User with this email already exists');
      }
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    try {
      const result = await db.query(
        'SELECT * FROM users WHERE id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return new User(result.rows[0]);
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    try {
      const result = await db.query(
        'SELECT * FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return new User(result.rows[0]);
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  // Verify password
  async verifyPassword(password) {
    try {
      return await bcrypt.compare(password, this.password);
    } catch (error) {
      logger.error('Error verifying password:', error);
      return false;
    }
  }

  // Update user
  async update(updateData) {
    const allowedFields = ['first_name', 'last_name', 'profile_picture', 'preferences'];
    const updates = [];
    const values = [];
    let paramCount = 1;

    // Build dynamic update query
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    }

    if (updates.length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    updates.push(`updated_at = NOW()`);
    values.push(this.id);

    try {
      const result = await db.query(
        `UPDATE users SET ${updates.join(', ')} WHERE id = $${paramCount} 
         RETURNING id, email, first_name, last_name, role, is_active, profile_picture, preferences, created_at, updated_at`,
        values
      );

      if (result.rows.length === 0) {
        throw new NotFoundError('User not found');
      }

      // Update current instance
      const updatedUser = new User(result.rows[0]);
      Object.assign(this, updatedUser);

      logger.logUserAction(this.id, 'USER_UPDATED', updateData);

      return this;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    // Verify current password
    const isCurrentPasswordValid = await this.verifyPassword(currentPassword);
    if (!isCurrentPasswordValid) {
      throw new ValidationError('Current password is incorrect');
    }

    // Validate new password
    if (newPassword.length < 8) {
      throw new ValidationError('New password must be at least 8 characters long');
    }

    try {
      // Hash new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password in database
      await db.query(
        'UPDATE users SET password = $1, updated_at = NOW() WHERE id = $2',
        [hashedPassword, this.id]
      );

      // Cache password change timestamp for token invalidation
      const changeTimestamp = Math.floor(Date.now() / 1000);
      await db.cache.set(`pwd_changed:${this.id}`, changeTimestamp, 7 * 24 * 3600);

      logger.logUserAction(this.id, 'PASSWORD_CHANGED');

      return true;
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }

  // Deactivate user
  async deactivate() {
    try {
      await db.query(
        'UPDATE users SET is_active = false, updated_at = NOW() WHERE id = $1',
        [this.id]
      );

      this.isActive = false;
      
      logger.logUserAction(this.id, 'USER_DEACTIVATED');

      return this;
    } catch (error) {
      logger.error('Error deactivating user:', error);
      throw error;
    }
  }

  // Get user's learning progress
  async getLearningProgress() {
    try {
      const result = await db.query(
        `SELECT 
          COUNT(DISTINCT lp.id) as total_paths,
          COUNT(DISTINCT CASE WHEN lp.status = 'completed' THEN lp.id END) as completed_paths,
          COUNT(DISTINCT er.id) as total_exercises,
          COUNT(DISTINCT CASE WHEN er.is_correct = true THEN er.id END) as correct_exercises,
          AVG(CASE WHEN er.is_correct = true THEN 1.0 ELSE 0.0 END) as accuracy_rate
        FROM users u
        LEFT JOIN learning_paths lp ON u.id = lp.user_id
        LEFT JOIN exercise_responses er ON u.id = er.user_id
        WHERE u.id = $1
        GROUP BY u.id`,
        [this.id]
      );

      return result.rows[0] || {
        total_paths: 0,
        completed_paths: 0,
        total_exercises: 0,
        correct_exercises: 0,
        accuracy_rate: 0,
      };
    } catch (error) {
      logger.error('Error getting learning progress:', error);
      throw error;
    }
  }

  // Convert to JSON (exclude sensitive data)
  toJSON() {
    return {
      id: this.id,
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      role: this.role,
      isActive: this.isActive,
      profilePicture: this.profilePicture,
      preferences: this.preferences,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}

module.exports = User;
