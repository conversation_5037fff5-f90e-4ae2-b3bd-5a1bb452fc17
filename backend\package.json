{"name": "adaptiq-backend", "version": "1.0.0", "description": "Backend API for AdaptIQ Adaptive Learning Platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest --coverage", "test:watch": "jest --watch", "test:integration": "jest --testPathPattern=tests/integration", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "npm run db:rollback && npm run db:migrate && npm run db:seed", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write \"src/**/*.js\"", "docs": "jsdoc -c jsdoc.conf.json", "build": "babel src -d dist", "docker:build": "docker build -t adaptiq-backend .", "docker:run": "docker run -p 5000:5000 adaptiq-backend"}, "keywords": ["adaptive-learning", "bayesian-knowledge-tracing", "spaced-repetition", "education-api", "lti-integration"], "author": {"name": "<PERSON>", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "knex": "^3.0.1", "redis": "^4.6.10", "ioredis": "^5.3.2", "nodemailer": "^6.9.7", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "joi": "^17.11.0", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.6.2", "xml2js": "^0.6.2", "crypto": "^1.0.1", "mathjs": "^12.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.57.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.2.5", "jsdoc": "^4.0.2", "@babel/cli": "^7.23.4", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!src/config/*.js"], "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}