{"name": "adaptiq-algorithms", "version": "1.0.0", "description": "Core algorithms for AdaptIQ: Bayesian Knowledge Tracing and Spaced Repetition System", "main": "index.js", "scripts": {"test": "jest --coverage", "test:watch": "jest --watch", "test:bkt": "jest --testPathPattern=bayesian-knowledge-tracing", "test:srs": "jest --testPathPattern=spaced-repetition", "test:adaptive": "jest --testPathPattern=adaptive-engine", "benchmark": "node benchmarks/run-benchmarks.js", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "format": "prettier --write \"**/*.js\"", "docs": "jsdoc -c jsdoc.conf.json", "build": "babel src -d dist", "validate": "npm run lint && npm run test"}, "keywords": ["bayesian-knowledge-tracing", "spaced-repetition", "adaptive-learning", "machine-learning", "education-algorithms", "anki-algorithm", "sm2-algorithm"], "author": {"name": "<PERSON>", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "dependencies": {"mathjs": "^12.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.1", "gaussian": "^1.3.0", "ml-matrix": "^6.10.7", "simple-statistics": "^7.8.3"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.57.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.2.5", "jsdoc": "^4.0.2", "@babel/cli": "^7.23.4", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0", "benchmark": "^2.1.4"}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/coverage/**", "!**/dist/**", "!**/benchmarks/**"], "testMatch": ["**/tests/**/*.test.js", "**/__tests__/**/*.test.js"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}