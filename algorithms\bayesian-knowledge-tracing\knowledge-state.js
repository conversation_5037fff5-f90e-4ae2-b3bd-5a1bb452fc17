/**
 * Knowledge State Management
 * 
 * This module provides utilities for managing and persisting knowledge states
 * in the Bayesian Knowledge Tracing system. It handles the interface between
 * the BKT algorithm and the database.
 */

const BayesianKnowledgeTracing = require('./bkt-core');
const { db } = require('../../backend/src/config/database');
const logger = require('../../backend/src/utils/logger');

class KnowledgeStateManager {
  constructor(options = {}) {
    this.bkt = new BayesianKnowledgeTracing(options);
    this.cacheTimeout = options.cacheTimeout || 3600; // 1 hour
  }

  /**
   * Get or create knowledge state for a user-concept pair
   * @param {string} userId - User ID
   * @param {string} conceptId - Concept ID
   * @param {Object} customParams - Custom BKT parameters for this concept
   * @returns {Object} Knowledge state
   */
  async getKnowledgeState(userId, conceptId, customParams = {}) {
    const cacheKey = `knowledge_state:${userId}:${conceptId}`;
    
    try {
      // Try to get from cache first
      let knowledgeState = await db.cache.get(cacheKey);
      
      if (!knowledgeState) {
        // Get from database
        const result = await db.query(
          `SELECT * FROM knowledge_states WHERE user_id = $1 AND concept_id = $2`,
          [userId, conceptId]
        );

        if (result.rows.length > 0) {
          knowledgeState = this.dbRowToKnowledgeState(result.rows[0]);
        } else {
          // Create new knowledge state
          knowledgeState = await this.createKnowledgeState(userId, conceptId, customParams);
        }

        // Cache the result
        await db.cache.set(cacheKey, knowledgeState, this.cacheTimeout);
      }

      return knowledgeState;
    } catch (error) {
      logger.error('Error getting knowledge state:', error);
      throw error;
    }
  }

  /**
   * Create a new knowledge state
   * @param {string} userId - User ID
   * @param {string} conceptId - Concept ID
   * @param {Object} customParams - Custom BKT parameters
   * @returns {Object} New knowledge state
   */
  async createKnowledgeState(userId, conceptId, customParams = {}) {
    try {
      // Initialize knowledge state with BKT
      const initialState = this.bkt.initializeKnowledgeState(customParams);

      // Save to database
      const result = await db.query(
        `INSERT INTO knowledge_states (
          user_id, concept_id, mastery_probability, prior_knowledge,
          transit_probability, slip_probability, guess_probability,
          attempts_count, correct_attempts, mastery_achieved
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *`,
        [
          userId,
          conceptId,
          initialState.masteryProbability,
          initialState.priorKnowledge,
          initialState.transitProbability,
          initialState.slipProbability,
          initialState.guessProbability,
          initialState.attemptsCount,
          initialState.correctAttempts,
          initialState.masteryAchieved,
        ]
      );

      const knowledgeState = this.dbRowToKnowledgeState(result.rows[0]);
      
      logger.logUserAction(userId, 'KNOWLEDGE_STATE_CREATED', {
        conceptId,
        initialMastery: initialState.masteryProbability,
      });

      return knowledgeState;
    } catch (error) {
      logger.error('Error creating knowledge state:', error);
      throw error;
    }
  }

  /**
   * Update knowledge state based on exercise response
   * @param {string} userId - User ID
   * @param {string} conceptId - Concept ID
   * @param {boolean} isCorrect - Whether the response was correct
   * @param {Object} responseData - Additional response data
   * @returns {Object} Updated knowledge state
   */
  async updateKnowledgeState(userId, conceptId, isCorrect, responseData = {}) {
    const startTime = Date.now();

    try {
      // Get current knowledge state
      const currentState = await this.getKnowledgeState(userId, conceptId);

      // Update using BKT algorithm
      const updatedState = this.bkt.updateKnowledgeState(currentState, isCorrect, responseData);

      // Save to database
      await db.query(
        `UPDATE knowledge_states SET
          mastery_probability = $1,
          attempts_count = $2,
          correct_attempts = $3,
          mastery_achieved = $4,
          mastery_achieved_at = $5,
          last_attempt_at = NOW(),
          updated_at = NOW()
        WHERE user_id = $6 AND concept_id = $7`,
        [
          updatedState.masteryProbability,
          updatedState.attemptsCount,
          updatedState.correctAttempts,
          updatedState.masteryAchieved,
          updatedState.masteryAchievedAt,
          userId,
          conceptId,
        ]
      );

      // Update cache
      const cacheKey = `knowledge_state:${userId}:${conceptId}`;
      await db.cache.set(cacheKey, updatedState, this.cacheTimeout);

      // Log the update
      const executionTime = Date.now() - startTime;
      logger.logUserAction(userId, 'KNOWLEDGE_STATE_UPDATED', {
        conceptId,
        isCorrect,
        previousMastery: currentState.masteryProbability,
        newMastery: updatedState.masteryProbability,
        masteryAchieved: updatedState.masteryAchieved,
        executionTime,
      });

      return updatedState;
    } catch (error) {
      logger.error('Error updating knowledge state:', error);
      throw error;
    }
  }

  /**
   * Get knowledge states for multiple concepts
   * @param {string} userId - User ID
   * @param {Array} conceptIds - Array of concept IDs
   * @returns {Object} Map of concept ID to knowledge state
   */
  async getMultipleKnowledgeStates(userId, conceptIds) {
    try {
      const knowledgeStates = {};
      
      // Try to get from cache first
      const cachePromises = conceptIds.map(conceptId => {
        const cacheKey = `knowledge_state:${userId}:${conceptId}`;
        return db.cache.get(cacheKey).then(state => ({ conceptId, state }));
      });

      const cachedResults = await Promise.all(cachePromises);
      const missingConceptIds = [];

      cachedResults.forEach(({ conceptId, state }) => {
        if (state) {
          knowledgeStates[conceptId] = state;
        } else {
          missingConceptIds.push(conceptId);
        }
      });

      // Get missing states from database
      if (missingConceptIds.length > 0) {
        const result = await db.query(
          `SELECT * FROM knowledge_states 
           WHERE user_id = $1 AND concept_id = ANY($2)`,
          [userId, missingConceptIds]
        );

        const dbStates = {};
        result.rows.forEach(row => {
          const state = this.dbRowToKnowledgeState(row);
          dbStates[row.concept_id] = state;
          knowledgeStates[row.concept_id] = state;
        });

        // Create states for concepts that don't exist yet
        const stillMissingIds = missingConceptIds.filter(id => !dbStates[id]);
        for (const conceptId of stillMissingIds) {
          const newState = await this.createKnowledgeState(userId, conceptId);
          knowledgeStates[conceptId] = newState;
        }

        // Cache the results
        const cachePromises = Object.entries(dbStates).map(([conceptId, state]) => {
          const cacheKey = `knowledge_state:${userId}:${conceptId}`;
          return db.cache.set(cacheKey, state, this.cacheTimeout);
        });
        await Promise.all(cachePromises);
      }

      return knowledgeStates;
    } catch (error) {
      logger.error('Error getting multiple knowledge states:', error);
      throw error;
    }
  }

  /**
   * Get mastery summary for a user
   * @param {string} userId - User ID
   * @param {string} subjectId - Optional subject ID to filter by
   * @returns {Object} Mastery summary
   */
  async getMasterySummary(userId, subjectId = null) {
    try {
      let query = `
        SELECT 
          ks.*,
          c.name as concept_name,
          c.subject_id,
          s.name as subject_name
        FROM knowledge_states ks
        JOIN concepts c ON ks.concept_id = c.id
        JOIN subjects s ON c.subject_id = s.id
        WHERE ks.user_id = $1
      `;
      
      const params = [userId];
      
      if (subjectId) {
        query += ' AND c.subject_id = $2';
        params.push(subjectId);
      }

      const result = await db.query(query, params);

      const summary = {
        totalConcepts: result.rows.length,
        masteredConcepts: 0,
        averageMastery: 0,
        conceptDetails: [],
      };

      let totalMastery = 0;

      result.rows.forEach(row => {
        const state = this.dbRowToKnowledgeState(row);
        const masteryStatus = this.bkt.getMasteryStatus(state);

        if (masteryStatus.isMastered) {
          summary.masteredConcepts++;
        }

        totalMastery += state.masteryProbability;

        summary.conceptDetails.push({
          conceptId: row.concept_id,
          conceptName: row.concept_name,
          subjectId: row.subject_id,
          subjectName: row.subject_name,
          masteryStatus,
        });
      });

      summary.averageMastery = summary.totalConcepts > 0 ? totalMastery / summary.totalConcepts : 0;
      summary.masteryPercentage = summary.totalConcepts > 0 ? (summary.masteredConcepts / summary.totalConcepts) * 100 : 0;

      return summary;
    } catch (error) {
      logger.error('Error getting mastery summary:', error);
      throw error;
    }
  }

  /**
   * Predict performance for upcoming exercises
   * @param {string} userId - User ID
   * @param {Array} exerciseIds - Array of exercise IDs
   * @returns {Array} Performance predictions
   */
  async predictPerformance(userId, exerciseIds) {
    try {
      // Get exercise-concept mappings
      const result = await db.query(
        `SELECT e.id as exercise_id, e.concept_id, e.difficulty_level
         FROM exercises e
         WHERE e.id = ANY($1)`,
        [exerciseIds]
      );

      const exerciseConceptMap = {};
      const conceptIds = [];

      result.rows.forEach(row => {
        exerciseConceptMap[row.exercise_id] = {
          conceptId: row.concept_id,
          difficultyLevel: row.difficulty_level,
        };
        if (!conceptIds.includes(row.concept_id)) {
          conceptIds.push(row.concept_id);
        }
      });

      // Get knowledge states for all relevant concepts
      const knowledgeStates = await this.getMultipleKnowledgeStates(userId, conceptIds);

      // Generate predictions
      const predictions = exerciseIds.map(exerciseId => {
        const exerciseInfo = exerciseConceptMap[exerciseId];
        if (!exerciseInfo) {
          return {
            exerciseId,
            predictedCorrectProbability: 0.5,
            confidence: 'low',
            recommendation: 'unknown',
          };
        }

        const knowledgeState = knowledgeStates[exerciseInfo.conceptId];
        const correctProbability = this.bkt.predictCorrectProbability(knowledgeState);

        // Adjust for difficulty level
        const difficultyAdjustment = (exerciseInfo.difficultyLevel - 5) * 0.05;
        const adjustedProbability = Math.max(0.1, Math.min(0.9, correctProbability - difficultyAdjustment));

        let recommendation = 'practice';
        if (adjustedProbability > 0.8) {
          recommendation = 'ready';
        } else if (adjustedProbability < 0.3) {
          recommendation = 'review_prerequisites';
        }

        return {
          exerciseId,
          conceptId: exerciseInfo.conceptId,
          predictedCorrectProbability: adjustedProbability,
          masteryProbability: knowledgeState.masteryProbability,
          attemptsCount: knowledgeState.attemptsCount,
          confidence: knowledgeState.attemptsCount > 5 ? 'high' : 'medium',
          recommendation,
        };
      });

      return predictions;
    } catch (error) {
      logger.error('Error predicting performance:', error);
      throw error;
    }
  }

  /**
   * Convert database row to knowledge state object
   * @private
   */
  dbRowToKnowledgeState(row) {
    return {
      masteryProbability: parseFloat(row.mastery_probability),
      priorKnowledge: parseFloat(row.prior_knowledge),
      transitProbability: parseFloat(row.transit_probability),
      slipProbability: parseFloat(row.slip_probability),
      guessProbability: parseFloat(row.guess_probability),
      attemptsCount: row.attempts_count,
      correctAttempts: row.correct_attempts,
      masteryAchieved: row.mastery_achieved,
      masteryAchievedAt: row.mastery_achieved_at,
      lastAttemptAt: row.last_attempt_at,
      lastUpdated: row.updated_at,
    };
  }

  /**
   * Clear cache for a user's knowledge states
   * @param {string} userId - User ID
   */
  async clearUserCache(userId) {
    try {
      // This is a simplified implementation - in production, you'd want a more efficient way
      // to clear all keys matching a pattern
      const conceptResult = await db.query(
        'SELECT DISTINCT concept_id FROM knowledge_states WHERE user_id = $1',
        [userId]
      );

      const clearPromises = conceptResult.rows.map(row => {
        const cacheKey = `knowledge_state:${userId}:${row.concept_id}`;
        return db.cache.del(cacheKey);
      });

      await Promise.all(clearPromises);
    } catch (error) {
      logger.error('Error clearing user cache:', error);
    }
  }
}

module.exports = KnowledgeStateManager;
