{"name": "adaptiq-learning-engine", "version": "1.0.0", "description": "An intelligent learning platform that personalizes educational content using Bayesian Knowledge Tracing and Spaced Repetition", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "start": "npm run backend:start", "backend:dev": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:algorithms": "cd algorithms && npm test", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "setup": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../algorithms && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "deploy": "npm run frontend:build && npm run docker:build && npm run docker:up"}, "keywords": ["adaptive-learning", "bayesian-knowledge-tracing", "spaced-repetition", "education", "personalized-learning", "lti-integration", "learning-analytics"], "author": {"name": "<PERSON>", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/AdaptIQ_AugmentCode.git"}, "bugs": {"url": "https://github.com/HectorTa1989/AdaptIQ_AugmentCode/issues"}, "homepage": "https://github.com/HectorTa1989/AdaptIQ_AugmentCode#readme", "devDependencies": {"concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "husky": "^9.0.11", "lint-staged": "^15.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}