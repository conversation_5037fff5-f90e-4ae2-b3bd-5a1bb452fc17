/**
 * Authentication Middleware
 * Handles JWT token verification and user authentication
 */

const jwt = require('jsonwebtoken');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const { AuthenticationError, AuthorizationError } = require('./errorHandler');

// Generate JWT token
const generateToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  });
};

// Generate refresh token
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  });
};

// Verify JWT token
const verifyToken = (token, secret = process.env.JWT_SECRET) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, secret, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded);
      }
    });
  });
};

// Main authentication middleware
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    let token;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    if (!token) {
      throw new AuthenticationError('Access token is required');
    }

    // Verify token
    const decoded = await verifyToken(token);

    // Check if token is blacklisted (logout)
    const isBlacklisted = await db.cache.exists(`blacklist:${token}`);
    if (isBlacklisted) {
      throw new AuthenticationError('Token has been invalidated');
    }

    // Get user from database
    const userResult = await db.query(
      'SELECT id, email, role, is_active, created_at, updated_at FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      throw new AuthenticationError('User not found');
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      throw new AuthenticationError('User account is deactivated');
    }

    // Check if password was changed after token was issued
    const passwordChangedAt = await db.cache.get(`pwd_changed:${user.id}`);
    if (passwordChangedAt && decoded.iat < passwordChangedAt) {
      throw new AuthenticationError('Password was changed. Please log in again.');
    }

    // Add user to request object
    req.user = user;
    req.token = token;

    // Log user activity
    logger.logUserAction(user.id, 'API_ACCESS', {
      endpoint: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      next(new AuthenticationError('Invalid token'));
    } else if (error.name === 'TokenExpiredError') {
      next(new AuthenticationError('Token has expired'));
    } else {
      next(error);
    }
  }
};

// Authorization middleware for specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    if (!roles.includes(req.user.role)) {
      return next(new AuthorizationError('Insufficient permissions'));
    }

    next();
  };
};

// Optional authentication (for public endpoints that can benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    let token;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      
      try {
        const decoded = await verifyToken(token);
        
        // Check if token is blacklisted
        const isBlacklisted = await db.cache.exists(`blacklist:${token}`);
        if (!isBlacklisted) {
          // Get user from database
          const userResult = await db.query(
            'SELECT id, email, role, is_active FROM users WHERE id = $1',
            [decoded.userId]
          );

          if (userResult.rows.length > 0 && userResult.rows[0].is_active) {
            req.user = userResult.rows[0];
            req.token = token;
          }
        }
      } catch (error) {
        // Ignore token errors in optional auth
        logger.debug('Optional auth token error:', error.message);
      }
    }

    next();
  } catch (error) {
    // Don't fail on optional auth errors
    next();
  }
};

// Refresh token middleware
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new AuthenticationError('Refresh token is required');
    }

    // Verify refresh token
    const decoded = await verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Check if refresh token is blacklisted
    const isBlacklisted = await db.cache.exists(`refresh_blacklist:${refreshToken}`);
    if (isBlacklisted) {
      throw new AuthenticationError('Refresh token has been invalidated');
    }

    // Get user from database
    const userResult = await db.query(
      'SELECT id, email, role, is_active FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      throw new AuthenticationError('User not found');
    }

    const user = userResult.rows[0];

    if (!user.is_active) {
      throw new AuthenticationError('User account is deactivated');
    }

    // Generate new tokens
    const newToken = generateToken({ userId: user.id, email: user.email });
    const newRefreshToken = generateRefreshToken({ userId: user.id });

    // Blacklist old refresh token
    await db.cache.set(`refresh_blacklist:${refreshToken}`, true, 7 * 24 * 3600);

    req.user = user;
    req.newToken = newToken;
    req.newRefreshToken = newRefreshToken;

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      next(new AuthenticationError('Invalid refresh token'));
    } else if (error.name === 'TokenExpiredError') {
      next(new AuthenticationError('Refresh token has expired'));
    } else {
      next(error);
    }
  }
};

// Logout middleware (blacklist token)
const logout = async (req, res, next) => {
  try {
    const token = req.token;
    
    if (token) {
      // Calculate remaining TTL for the token
      const decoded = jwt.decode(token);
      const now = Math.floor(Date.now() / 1000);
      const ttl = decoded.exp - now;

      if (ttl > 0) {
        // Blacklist the token for its remaining lifetime
        await db.cache.set(`blacklist:${token}`, true, ttl);
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  refreshToken,
  logout,
  generateToken,
  generateRefreshToken,
  verifyToken,
};
